#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import fs from 'fs/promises'
import path from 'path'

const prisma = new PrismaClient()

interface ExportedData {
  storeCodes: any[]
  customers: any[]
  orders: any[]
  invoices: any[]
  invoiceItems: any[]
  storePricing: any[]
  storePricingTiers: any[]
  defaultPricing: any[]
  defaultPricingTiers: any[]
  customerAddresses: any[]
  customerCommunications: any[]
  orderAttachments: any[]
  orderMetrics: any[]
  orderNotes: any[]
  orderStatusHistory: any[]
  orderTags: any[]
  orderTimeline: any[]
  invoiceAttachments: any[]
  invoiceCommunications: any[]
  invoicePayments: any[]
  storeConfigurations: any[]
  exportedAt: string
  totalRecords: number
}

async function exportData(): Promise<void> {
  try {
    console.log('🚀 Starting Prisma data export...')

    // Export all data from Prisma
    const [
      storeCodes,
      customers,
      orders,
      invoices,
      invoiceItems,
      storePricing,
      storePricingTiers,
      defaultPricing,
      defaultPricingTiers,
      customerAddresses,
      customerCommunications,
      orderAttachments,
      orderMetrics,
      orderNotes,
      orderStatusHistory,
      orderTags,
      orderTimeline,
      invoiceAttachments,
      invoiceCommunications,
      invoicePayments,
      storeConfigurations
    ] = await Promise.all([
      prisma.storeCode.findMany({
        include: {
          orders: true,
          parentStore: true,
          childStores: true,
          configurations: true,
          pricing: {
            include: {
              pricingTiers: true
            }
          }
        }
      }),
      prisma.customer.findMany({
        include: {
          addresses: true,
          communications: true,
          invoices: true,
          orders: true
        }
      }),
      prisma.order.findMany({
        include: {
          storeCode: true,
          customer: true,
          invoiceItems: true,
          attachments: true,
          metrics: true,
          notes: true,
          statusHistory: true,
          tags: true,
          timeline: true
        }
      }),
      prisma.invoice.findMany({
        include: {
          customer: true,
          invoiceItems: {
            include: {
              order: true
            }
          },
          payments: true,
          attachments: true,
          communications: true
        }
      }),
      prisma.invoiceItem.findMany({
        include: {
          invoice: true,
          order: true
        }
      }),
      prisma.storePricing.findMany({
        include: {
          storeCode: true,
          pricingTiers: true
        }
      }),
      prisma.storePricingTier.findMany({
        include: {
          storePricing: true
        }
      }),
      prisma.defaultPricing.findMany({
        include: {
          pricingTiers: true
        }
      }),
      prisma.defaultPricingTier.findMany({
        include: {
          defaultPricing: true
        }
      }),
      prisma.customerAddress.findMany({
        include: {
          customer: true
        }
      }),
      prisma.customerCommunication.findMany({
        include: {
          customer: true
        }
      }),
      prisma.orderAttachment.findMany({
        include: {
          order: true
        }
      }),
      prisma.orderMetrics.findMany({
        include: {
          order: true
        }
      }),
      prisma.orderNote.findMany({
        include: {
          order: true
        }
      }),
      prisma.orderStatusHistory.findMany({
        include: {
          order: true
        }
      }),
      prisma.orderTag.findMany({
        include: {
          order: true
        }
      }),
      prisma.orderTimeline.findMany({
        include: {
          order: true
        }
      }),
      prisma.invoiceAttachment.findMany({
        include: {
          invoice: true
        }
      }),
      prisma.invoiceCommunication.findMany({
        include: {
          invoice: true
        }
      }),
      prisma.invoicePayment.findMany({
        include: {
          invoice: true
        }
      }),
      prisma.storeConfiguration.findMany({
        include: {
          storeCode: true
        }
      })
    ])

    const exportedData: ExportedData = {
      storeCodes,
      customers,
      orders,
      invoices,
      invoiceItems,
      storePricing,
      storePricingTiers,
      defaultPricing,
      defaultPricingTiers,
      customerAddresses,
      customerCommunications,
      orderAttachments,
      orderMetrics,
      orderNotes,
      orderStatusHistory,
      orderTags,
      orderTimeline,
      invoiceAttachments,
      invoiceCommunications,
      invoicePayments,
      storeConfigurations,
      exportedAt: new Date().toISOString(),
      totalRecords: storeCodes.length + customers.length + orders.length + invoices.length +
                   invoiceItems.length + storePricing.length + storePricingTiers.length +
                   defaultPricing.length + defaultPricingTiers.length +
                   customerAddresses.length + customerCommunications.length + orderAttachments.length +
                   orderMetrics.length + orderNotes.length + orderStatusHistory.length +
                   orderTags.length + orderTimeline.length + invoiceAttachments.length +
                   invoiceCommunications.length + invoicePayments.length + storeConfigurations.length
    }

    // Create exports directory if it doesn't exist
    const exportsDir = path.join(process.cwd(), 'exports')
    await fs.mkdir(exportsDir, { recursive: true })

    // Save to JSON file with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `prisma-export-${timestamp}.json`
    const filepath = path.join(exportsDir, filename)

    await fs.writeFile(filepath, JSON.stringify(exportedData, null, 2))

    console.log('✅ Data export completed successfully!')
    console.log(`📁 Export saved to: ${filepath}`)
    console.log(`📊 Total records exported: ${exportedData.totalRecords}`)
    console.log('\n📋 Export Summary:')
    console.log(`  - Store Codes: ${storeCodes.length}`)
    console.log(`  - Customers: ${customers.length}`)
    console.log(`  - Orders: ${orders.length}`)
    console.log(`  - Invoices: ${invoices.length}`)
    console.log(`  - Invoice Items: ${invoiceItems.length}`)
    console.log(`  - Store Pricing: ${storePricing.length}`)
    console.log(`  - Store Pricing Tiers: ${storePricingTiers.length}`)
    console.log(`  - Default Pricing: ${defaultPricing.length}`)
    console.log(`  - Default Pricing Tiers: ${defaultPricingTiers.length}`)
    console.log(`  - Customer Addresses: ${customerAddresses.length}`)
    console.log(`  - Customer Communications: ${customerCommunications.length}`)
    console.log(`  - Order Attachments: ${orderAttachments.length}`)
    console.log(`  - Order Metrics: ${orderMetrics.length}`)
    console.log(`  - Order Notes: ${orderNotes.length}`)
    console.log(`  - Order Status History: ${orderStatusHistory.length}`)
    console.log(`  - Order Tags: ${orderTags.length}`)
    console.log(`  - Order Timeline: ${orderTimeline.length}`)
    console.log(`  - Invoice Attachments: ${invoiceAttachments.length}`)
    console.log(`  - Invoice Communications: ${invoiceCommunications.length}`)
    console.log(`  - Invoice Payments: ${invoicePayments.length}`)
    console.log(`  - Store Configurations: ${storeConfigurations.length}`)

    // Also create a simplified export for easier import
    const simplifiedData = {
      storeCodes: storeCodes.map(sc => ({
        id: sc.id,
        code: sc.code,
        name: sc.name,
        storeType: sc.storeType,
        status: sc.status,
        address: sc.address,
        city: sc.city,
        state: sc.state,
        postalCode: sc.postalCode,
        country: sc.country,
        phone: sc.phone,
        email: sc.email,
        website: sc.website,
        managerName: sc.managerName,
        managerEmail: sc.managerEmail,
        contactPerson: sc.contactPerson,
        operatingHours: sc.operatingHours,
        timezone: sc.timezone,
        isOpen: sc.isOpen,
        allowsPickup: sc.allowsPickup,
        allowsDelivery: sc.allowsDelivery,
        deliveryRadius: sc.deliveryRadius,
        minimumOrder: sc.minimumOrder,
        serviceFee: sc.serviceFee,
        averageProcessingTime: sc.averageProcessingTime,
        capacity: sc.capacity,
        priority: sc.priority,
        totalOrders: sc.totalOrders,
        totalRevenue: sc.totalRevenue,
        averageOrderValue: sc.averageOrderValue,
        notes: sc.notes,
        internalNotes: sc.internalNotes,
        specialInstructions: sc.specialInstructions,
        externalStoreId: sc.externalStoreId,
        apiEndpoint: sc.apiEndpoint,
        apiKey: sc.apiKey,
        parentStoreId: sc.parentStoreId,
        createdAt: sc.createdAt,
        updatedAt: sc.updatedAt
      })),
      customers: customers.map(c => ({
        id: c.id,
        name: c.name,
        customerNumber: c.customerNumber,
        customerType: c.customerType,
        status: c.status,
        email: c.email,
        phone: c.phone,
        alternatePhone: c.alternatePhone,
        website: c.website,
        address: c.address,
        city: c.city,
        state: c.state,
        postalCode: c.postalCode,
        country: c.country,
        businessName: c.businessName,
        taxId: c.taxId,
        businessType: c.businessType,
        preferredDeliveryMethod: c.preferredDeliveryMethod,
        preferredPaymentMethod: c.preferredPaymentMethod,
        creditLimit: c.creditLimit,
        paymentTerms: c.paymentTerms,
        discountRate: c.discountRate,
        segment: c.segment,
        loyaltyTier: c.loyaltyTier,
        loyaltyPoints: c.loyaltyPoints,
        assignedSalesRep: c.assignedSalesRep,
        accountManager: c.accountManager,
        referredBy: c.referredBy,
        firstOrderDate: c.firstOrderDate,
        lastOrderDate: c.lastOrderDate,
        lastContactDate: c.lastContactDate,
        totalOrders: c.totalOrders,
        totalSpent: c.totalSpent,
        averageOrderValue: c.averageOrderValue,
        notes: c.notes,
        internalNotes: c.internalNotes,
        createdAt: c.createdAt,
        updatedAt: c.updatedAt
      })),
      orders: orders.map(o => ({
        id: o.id,
        productName: o.productName,
        quantity: o.quantity,
        usageUnit: o.usageUnit,
        comment: o.comment,
        imageFilename: o.imageFilename,
        storePrice: o.storePrice,
        pasabuyFee: o.pasabuyFee,
        customerPrice: o.customerPrice,
        isBought: o.isBought,
        packingStatus: o.packingStatus,
        orderNumber: o.orderNumber,
        priority: o.priority,
        category: o.category,
        brand: o.brand,
        model: o.model,
        sku: o.sku,
        barcode: o.barcode,
        originalPrice: o.originalPrice,
        discountAmount: o.discountAmount,
        discountType: o.discountType,
        discountReason: o.discountReason,
        taxAmount: o.taxAmount,
        taxRate: o.taxRate,
        source: o.source,
        urgency: o.urgency,
        internalNotes: o.internalNotes,
        customerNotes: o.customerNotes,
        specialInstructions: o.specialInstructions,
        estimatedDelivery: o.estimatedDelivery,
        requestedDelivery: o.requestedDelivery,
        completedAt: o.completedAt,
        cancelledAt: o.cancelledAt,
        cancellationReason: o.cancellationReason,
        parentOrderId: o.parentOrderId,
        orderGroupId: o.orderGroupId,
        deliveryStatus: o.deliveryStatus,
        deliveryDate: o.deliveryDate,
        deliveryMethod: o.deliveryMethod,
        trackingNumber: o.trackingNumber,
        deliveryNotes: o.deliveryNotes,
        storeCodeId: o.storeCodeId,
        customerId: o.customerId,
        createdAt: o.createdAt,
        updatedAt: o.updatedAt
      }))
    }

    const simplifiedFilename = `prisma-simplified-${timestamp}.json`
    const simplifiedFilepath = path.join(exportsDir, simplifiedFilename)
    await fs.writeFile(simplifiedFilepath, JSON.stringify(simplifiedData, null, 2))

    console.log(`📁 Simplified export saved to: ${simplifiedFilepath}`)

  } catch (error) {
    console.error('❌ Error exporting data:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the export
exportData()
