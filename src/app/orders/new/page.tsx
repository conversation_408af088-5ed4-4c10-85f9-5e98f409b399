'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { OrderForm } from '@/components/forms/order-form'
import { useAppStore } from '@/lib/store'
import { LuPlus } from 'react-icons/lu'
import { useScrollToTop } from '@/hooks/use-scroll'

export default function NewOrderPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { addOrder, storeCodes, setStoreCodes, customers, setCustomers } = useAppStore()
  const [isLoading, setIsLoading] = useState(false)
  const [initialData, setInitialData] = useState<Record<string, string | undefined>>({})
  const [contextInfo, setContextInfo] = useState<{ storeCode?: { id: number; code: string; name?: string }, customer?: { id: number; name: string } }>({})

  // Automatically scroll to top when page loads
  useScrollToTop()

  useEffect(() => {
    // Load store codes and customers if not already loaded
    async function loadData() {
      const promises = []

      if (storeCodes.length === 0) {
        promises.push(
          fetch('/api/store-codes')
            .then(response => response.ok ? response.json() : [])
            .then(data => setStoreCodes(data))
            .catch(error => console.error('Error loading store codes:', error))
        )
      }

      if (customers.length === 0) {
        promises.push(
          fetch('/api/customers')
            .then(response => response.ok ? response.json() : [])
            .then(data => setCustomers(data))
            .catch(error => console.error('Error loading customers:', error))
        )
      }

      await Promise.all(promises)
    }

    loadData()
  }, [storeCodes.length, setStoreCodes, customers.length, setCustomers])

  useEffect(() => {
    // Pre-fill form data from URL parameters
    const storeCodeId = searchParams.get('storeCodeId')
    const resellerId = searchParams.get('resellerId')
    const storeCode = searchParams.get('storeCode') // Backward compatibility

    let resolvedStoreCodeId = storeCodeId
    let matchingStoreCode = null
    let matchingCustomer = null

    // Handle backward compatibility: convert storeCode to storeCodeId
    if (!storeCodeId && storeCode && storeCodes.length > 0) {
      matchingStoreCode = storeCodes.find(
        sc => sc.code.toLowerCase() === storeCode.toLowerCase()
      )
      if (matchingStoreCode) {
        resolvedStoreCodeId = matchingStoreCode.id.toString()
      }
    } else if (resolvedStoreCodeId && storeCodes.length > 0) {
      matchingStoreCode = storeCodes.find(
        sc => sc.id.toString() === resolvedStoreCodeId
      )
    }

    // Find matching customer
    if (resellerId && customers.length > 0) {
      matchingCustomer = customers.find(
        r => r.id.toString() === resellerId
      )
    }

    // Set initial data for form
    if (resolvedStoreCodeId || resellerId) {
      setInitialData({
        storeCodeId: resolvedStoreCodeId || undefined,
        resellerId: resellerId || undefined,
      })
    }

    // Set context info for display
    setContextInfo({
      storeCode: matchingStoreCode || undefined,
      customer: matchingCustomer || undefined,
    })
  }, [searchParams, storeCodes, customers])

  const handleSubmit = async (data: {
    productName: string
    quantity: number
    usageUnit?: string
    comment?: string
    storePrice: number
    pasabuyFee: number
    customerPrice: number
    storeCodeId?: string
    customerId?: string
    imageFile?: File
  }) => {
    try {
      setIsLoading(true)

      // Handle image upload if present
      let imageFilename = null
      if (data.imageFile) {
        const formData = new FormData()
        formData.append('image', data.imageFile)

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        if (uploadResponse.ok) {
          const uploadResult = await uploadResponse.json()
          imageFilename = uploadResult.filename
        }
      }

      // Create the order
      const orderData = {
        productName: data.productName,
        quantity: data.quantity,
        usageUnit: data.usageUnit || null,
        comment: data.comment || null,
        storePrice: data.storePrice,
        pasabuyFee: data.pasabuyFee,
        customerPrice: data.customerPrice,
        storeCodeId: data.storeCodeId || null,
        customerId: data.customerId || null,
        isBought: false, // Default value for new items
        packingStatus: 'Not Packed', // Default value for new items
        imageFilename,
      }

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      })

      if (!response.ok) {
        throw new Error('Failed to create order')
      }

      const newOrder = await response.json()
      addOrder(newOrder)

      router.push('/orders')
    } catch (error) {
      console.error('Error creating order:', error)
      // You might want to show a toast notification here
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">Add New Order</h1>
          <div className="flex flex-col gap-1">
            <p className="text-muted-foreground">
              Add a new item to your pasabuy list.
            </p>
            {(contextInfo.storeCode || contextInfo.customer) && (
              <div className="flex items-center gap-2">
                {contextInfo.storeCode && (
                  <span className="text-sm">
                    Pre-filled for store: <span className="font-medium text-primary">{contextInfo.storeCode.code}</span>
                  </span>
                )}
                {contextInfo.customer && (
                  <span className="text-sm">
                    Pre-filled for customer: <span className="font-medium text-primary">{contextInfo.customer.name}</span>
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        <Link href="/orders/new/multi">
          <Button variant="outline" className="h-7 text-sm">
            <LuPlus className="h-4 w-4 mr-1" />
            Multiple Orders
          </Button>
        </Link>
      </div>

      <OrderForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isLoading}
        initialData={initialData}
      />
    </div>
  )
}
