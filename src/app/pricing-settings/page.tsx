'use client'

import { useEffect, useState, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { PageWrapper } from '@/components/layout/page-wrapper'
import { useScrollToTop } from '@/hooks/use-scroll'
import { LuCalculator, LuSave, LuInfo, LuStore } from 'react-icons/lu'
import { handleNumberInputChange } from '@/lib/utils'

const pricingSettingsSchema = z.object({
  markupType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']),
  markupValue: z.number().min(0, 'Markup value cannot be negative'),
  serviceFee: z.number().min(0, 'Pasabuy fee cannot be negative')
})

type PricingSettingsData = z.infer<typeof pricingSettingsSchema>

interface PricingSettings {
  id: number
  markupType: string
  markupValue: number
  serviceFee: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export default function PricingSettingsPage() {
  const [, setSettings] = useState<PricingSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  useScrollToTop()

  // Hide bottom navigation for focused experience
  useEffect(() => {
    document.body.style.paddingBottom = '0'
    return () => {
      document.body.style.paddingBottom = '4rem'
    }
  }, [])

  const form = useForm<PricingSettingsData>({
    resolver: zodResolver(pricingSettingsSchema),
    defaultValues: {
      markupType: 'PERCENTAGE',
      markupValue: 100,
      serviceFee: 20
    }
  })

  useEffect(() => {
    fetchSettings()
  }, [fetchSettings])

  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/pricing-settings')
      if (!response.ok) {
        throw new Error('Failed to fetch pricing settings')
      }
      
      const data = await response.json()
      setSettings(data)
      
      // Update form with fetched data
      form.reset({
        markupType: data.markupType as 'PERCENTAGE' | 'FIXED_AMOUNT',
        markupValue: data.markupValue,
        serviceFee: data.serviceFee
      })
    } catch (error) {
      console.error('Error fetching pricing settings:', error)
      setError('Failed to load pricing settings')
    } finally {
      setLoading(false)
    }
  }, [form])

  const handleSubmit = async (data: PricingSettingsData) => {
    try {
      setSaving(true)
      setError(null)
      setSuccess(false)

      const response = await fetch('/api/pricing-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update pricing settings')
      }

      const updatedSettings = await response.json()
      setSettings(updatedSettings)
      setSuccess(true)
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000)
    } catch (error) {
      console.error('Error updating pricing settings:', error)
      setError(error instanceof Error ? error.message : 'An unexpected error occurred')
    } finally {
      setSaving(false)
    }
  }

  // Calculate example pricing
  const exampleStorePrice = 500
  const markupType = form.watch('markupType')
  const markupValue = form.watch('markupValue') || 0
  const serviceFee = form.watch('serviceFee') || 0

  const calculateExample = () => {
    let markup = 0
    if (markupType === 'PERCENTAGE') {
      markup = exampleStorePrice * (markupValue / 100)
    } else {
      markup = markupValue
    }
    const pasabuyFee = serviceFee
    const customerPrice = exampleStorePrice + markup - serviceFee
    
    return { markup, pasabuyFee, customerPrice }
  }

  const example = calculateExample()

  if (loading) {
    return (
      <PageWrapper>
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Loading pricing settings...</div>
        </div>
      </PageWrapper>
    )
  }

  return (
    <PageWrapper>
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-semibold tracking-tight flex items-center justify-center gap-2">
            <LuCalculator className="h-6 w-6" />
            Pricing Settings
          </h1>
          <p className="text-muted-foreground mt-2">
            Configure automatic pricing calculations for all orders
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <div className="text-destructive text-sm">{error}</div>
            </CardContent>
          </Card>
        )}

        {/* Success Display */}
        {success && (
          <Card className="border-green-500 bg-green-50">
            <CardContent className="p-4">
              <div className="text-green-700 text-sm">Pricing settings updated successfully!</div>
            </CardContent>
          </Card>
        )}

        {/* Settings Form */}
        <Card>
          <CardHeader>
            <CardTitle>Pricing Configuration</CardTitle>
            <CardDescription>
              Set up how customer prices are automatically calculated from store prices. For advanced tiered pricing with range-based Pasabuy fees, use the Store Pricing management.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="markupType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Markup Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="min-h-[44px]">
                            <SelectValue placeholder="Select markup type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="PERCENTAGE">Percentage (%)</SelectItem>
                          <SelectItem value="FIXED_AMOUNT">Fixed Amount (₱)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose how to calculate the markup on store prices
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="markupValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {markupType === 'PERCENTAGE' ? 'Markup Percentage' : 'Fixed Markup Amount (₱)'}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step={markupType === 'PERCENTAGE' ? '1' : '0.01'}
                          placeholder={markupType === 'PERCENTAGE' ? '100' : '50.00'}
                          className="min-h-[44px]"
                          {...field}
                          onChange={(e) => field.onChange(handleNumberInputChange(e.target.value, {
                            min: 0,
                            defaultValue: 0
                          }))}
                          onBlur={(e) => {
                            // Convert to number on blur if it's a valid string
                            const value = e.target.value
                            if (value === '' || isNaN(parseFloat(value))) {
                              field.onChange(0)
                            }
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        {markupType === 'PERCENTAGE' 
                          ? 'Percentage to add to store price (e.g., 100% doubles the price)'
                          : 'Fixed amount to add to store price'
                        }
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="serviceFee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pasabuy Fee (₱)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="20.00"
                          className="min-h-[44px]"
                          {...field}
                          onChange={(e) => field.onChange(handleNumberInputChange(e.target.value, {
                            min: 0,
                            defaultValue: 0
                          }))}
                          onBlur={(e) => {
                            // Convert to number on blur if it's a valid string
                            const value = e.target.value
                            if (value === '' || isNaN(parseFloat(value))) {
                              field.onChange(0)
                            }
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Fixed fee added to all orders (in addition to markup)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={saving}
                  className="w-full min-h-[48px] text-base font-medium"
                >
                  {saving ? (
                    <>
                      <LuCalculator className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <LuSave className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Pricing Preview */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900 flex items-center gap-2">
              <LuInfo className="h-5 w-5" />
              Pricing Preview
            </CardTitle>
            <CardDescription className="text-blue-700">
              Example calculation with a ₱{exampleStorePrice} store price
            </CardDescription>
          </CardHeader>
          <CardContent className="text-blue-800">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Store Price:</span>
                <span className="font-medium">₱{exampleStorePrice.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>
                  {markupType === 'PERCENTAGE' 
                    ? `Markup (${markupValue}%):`
                    : 'Fixed Markup:'
                  }
                </span>
                <span className="font-medium text-green-700">+₱{example.markup.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Pasabuy Fee:</span>
                <span className="font-medium text-green-700">+₱{serviceFee.toFixed(2)}</span>
              </div>
              <hr className="border-blue-300" />
              <div className="flex justify-between text-lg font-bold">
                <span>Customer Price:</span>
                <span className="text-blue-900">₱{example.customerPrice.toFixed(2)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Advanced Pricing Link */}
        <Card className="border-blue-200 bg-blue-50/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-blue-900">Advanced Tiered Pricing</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Configure different markup amounts and Pasabuy fees based on price ranges for each store.
                </p>
              </div>
              <Button
                variant="outline"
                className="border-blue-300 text-blue-700 hover:bg-blue-100"
                onClick={() => window.location.href = '/store-pricing'}
              >
                <LuStore className="h-4 w-4 mr-2" />
                Manage Store Pricing
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageWrapper>
  )
}
