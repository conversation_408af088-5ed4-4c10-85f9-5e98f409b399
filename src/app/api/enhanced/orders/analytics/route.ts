import { NextRequest, NextResponse } from 'next/server'
import { EnhancedOrderService } from '@/lib/enhanced-order-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters for analytics
    const filters: Record<string, unknown> = {}

    if (searchParams.get('customerId')) filters.customerId = parseInt(searchParams.get('customerId')!)
    if (searchParams.get('storeCodeId')) filters.storeCodeId = parseInt(searchParams.get('storeCodeId')!)
    if (searchParams.get('createdAfter')) {
      filters.createdAfter = new Date(searchParams.get('createdAfter')!)
    }
    if (searchParams.get('createdBefore')) {
      filters.createdBefore = new Date(searchParams.get('createdBefore')!)
    }

    const analytics = await EnhancedOrderService.getOrderAnalytics(filters)

    return NextResponse.json(analytics)
  } catch (error: unknown) {
    console.error('Error fetching order analytics:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching order analytics' },
      { status: 500 }
    )
  }
}
