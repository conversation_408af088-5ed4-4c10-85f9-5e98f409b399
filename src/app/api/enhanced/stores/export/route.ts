import { NextRequest, NextResponse } from 'next/server'
import { EnhancedStoreService } from '@/lib/enhanced-store-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse export options
    const format = (searchParams.get('format') || 'EXCEL') as 'CSV' | 'EXCEL'
    const includeMetrics = searchParams.get('includeMetrics') === 'true'
    const includeConfigurations = searchParams.get('includeConfigurations') === 'true'
    
    // Parse filters (same as search endpoint)
    const filters: Record<string, unknown> = {}
    if (searchParams.get('code')) filters.code = searchParams.get('code')
    if (searchParams.get('name')) filters.name = searchParams.get('name')
    if (searchParams.get('storeType')) {
      filters.storeType = searchParams.get('storeType')!.split(',')
    }
    if (searchParams.get('status')) {
      filters.status = searchParams.get('status')!.split(',')
    }
    if (searchParams.get('region')) {
      filters.region = searchParams.get('region')!.split(',')
    }
    if (searchParams.get('city')) {
      filters.city = searchParams.get('city')!.split(',')
    }
    if (searchParams.get('storeGroup')) filters.storeGroup = searchParams.get('storeGroup')
    if (searchParams.get('isOpen')) filters.isOpen = searchParams.get('isOpen') === 'true'
    if (searchParams.get('hasOrders')) filters.hasOrders = searchParams.get('hasOrders') === 'true'
    if (searchParams.get('searchTerm')) filters.searchTerm = searchParams.get('searchTerm')

    // Parse fields
    const fields = searchParams.get('fields')?.split(',')

    const options = {
      format,
      includeMetrics,
      includeConfigurations,
      filters,
      fields
    }

    // Generate export
    const buffer = await EnhancedStoreService.exportStores(options)

    // Set response headers
    const filename = `stores_export_${new Date().toISOString().split('T')[0]}`
    const contentType = format === 'CSV' 
      ? 'text/csv' 
      : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    const fileExtension = format === 'CSV' ? 'csv' : 'xlsx'

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}.${fileExtension}"`,
        'Content-Length': buffer.length.toString(),
      },
    })
  } catch (error: unknown) {
    console.error('Error exporting stores:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error during export' },
      { status: 500 }
    )
  }
}
