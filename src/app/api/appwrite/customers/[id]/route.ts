import { NextRequest, NextResponse } from 'next/server'
import { appwriteCustomerService } from '@/lib/services/appwrite-customer-service'
import { authService } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params

    const customer = await appwriteCustomerService.getCustomerById(id, userId)

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(customer)
  } catch (error: unknown) {
    console.error('Error fetching customer:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching customer' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    const customer = await appwriteCustomerService.updateCustomer(id, body, userId)

    return NextResponse.json(customer)
  } catch (error: unknown) {
    console.error('Error updating customer:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    if (errorMessage.includes('already exists')) {
      return NextResponse.json(
        { error: 'Customer name already exists' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error updating customer' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params

    await appwriteCustomerService.deleteCustomer(id, userId)

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    console.error('Error deleting customer:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error deleting customer' },
      { status: 500 }
    )
  }
}
