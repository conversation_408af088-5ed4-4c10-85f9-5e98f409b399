import { NextRequest, NextResponse } from 'next/server'
import { appwriteCustomerService } from '@/lib/services/appwrite-customer-service'
import { authService } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')

    let customers
    if (search) {
      customers = await appwriteCustomerService.searchCustomers(search, userId)
    } else {
      customers = await appwriteCustomerService.getCustomersWithCounts(userId)
    }

    return NextResponse.json(customers)
  } catch (error: unknown) {
    console.error('Error fetching customers:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching customers' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name } = body

    if (!name) {
      return NextResponse.json(
        { error: 'Customer name is required' },
        { status: 400 }
      )
    }

    const customer = await appwriteCustomerService.createCustomer(body, userId)

    return NextResponse.json(customer, { status: 201 })
  } catch (error: unknown) {
    console.error('Error creating customer:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('already exists')) {
      return NextResponse.json(
        { error: 'Customer name already exists' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error creating customer' },
      { status: 500 }
    )
  }
}
