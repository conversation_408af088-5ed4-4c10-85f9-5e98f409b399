import { NextRequest, NextResponse } from 'next/server'
import { appwriteInvoiceService } from '@/lib/services/appwrite-invoice-service'
import { authService } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params

    const invoice = await appwriteInvoiceService.getInvoiceById(id, userId)

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(invoice)
  } catch (error: unknown) {
    console.error('Error fetching invoice:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching invoice' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    const invoice = await appwriteInvoiceService.updateInvoice(id, body, userId)

    return NextResponse.json(invoice)
  } catch (error: unknown) {
    console.error('Error updating invoice:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    if (errorMessage.includes('already exists')) {
      return NextResponse.json(
        { error: 'Invoice number already exists' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error updating invoice' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params

    await appwriteInvoiceService.deleteInvoice(id, userId)

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    console.error('Error deleting invoice:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error deleting invoice' },
      { status: 500 }
    )
  }
}
