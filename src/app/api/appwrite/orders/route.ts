import { NextRequest, NextResponse } from 'next/server'
import { appwriteOrderService } from '@/lib/services/appwrite-order-service'
import { authService } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const storeCodeId = searchParams.get('storeCodeId')
    const customerId = searchParams.get('customerId')
    const isBought = searchParams.get('isBought')
    const packingStatus = searchParams.get('packingStatus')
    const type = searchParams.get('type') // 'buy-list' or 'packing-list'

    let orders

    if (search) {
      orders = await appwriteOrderService.searchOrders(search, userId)
    } else if (type === 'buy-list') {
      orders = await appwriteOrderService.getBuyList(userId)
    } else if (type === 'packing-list') {
      orders = await appwriteOrderService.getPackingList(userId)
    } else if (storeCodeId) {
      orders = await appwriteOrderService.getOrdersByStoreCode(storeCodeId, userId)
    } else if (customerId) {
      orders = await appwriteOrderService.getOrdersByCustomer(customerId, userId)
    } else if (isBought !== null) {
      orders = await appwriteOrderService.getOrdersByStatus(
        isBought === 'true', 
        packingStatus || undefined, 
        userId
      )
    } else {
      orders = await appwriteOrderService.getOrders(userId)
    }

    return NextResponse.json(orders)
  } catch (error: unknown) {
    console.error('Error fetching orders:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { productName } = body

    if (!productName) {
      return NextResponse.json(
        { error: 'Product name is required' },
        { status: 400 }
      )
    }

    const order = await appwriteOrderService.createOrder(body, userId)

    return NextResponse.json(order, { status: 201 })
  } catch (error: unknown) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error creating order' },
      { status: 500 }
    )
  }
}
