import { NextRequest, NextResponse } from 'next/server'
import { appwriteStoreService } from '@/lib/services/appwrite-store-service'
import { authService } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')

    let storeCodes
    if (search) {
      storeCodes = await appwriteStoreService.searchStoreCodes(search, userId)
    } else {
      storeCodes = await appwriteStoreService.getStoreCodesWithCounts(userId)
    }

    return NextResponse.json(storeCodes)
  } catch (error: unknown) {
    console.error('Error fetching store codes:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching store codes' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { code, name } = body

    if (!code) {
      return NextResponse.json(
        { error: 'Store code is required' },
        { status: 400 }
      )
    }

    const storeCode = await appwriteStoreService.createStoreCode({
      code: code.trim(),
      name: name?.trim() || null
    }, userId)

    return NextResponse.json(storeCode, { status: 201 })
  } catch (error: unknown) {
    console.error('Error creating store code:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('already exists')) {
      return NextResponse.json(
        { error: 'Store code already exists' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error creating store code' },
      { status: 500 }
    )
  }
}
