import { Client, Databases, Storage, Account } from 'appwrite'
import { Client as NodeClient, Databases as NodeDatabases, Storage as NodeStorage, Users as NodeUsers } from 'node-appwrite'

// Client-side Appwrite configuration
export const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!)

// Server-side Appwrite configuration
export const serverClient = new NodeClient()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!)
  .setKey(process.env.APPWRITE_API_KEY!)

// Client-side services
export const databases = new Databases(client)
export const storage = new Storage(client)
export const account = new Account(client)

// Server-side services
export const serverDatabases = new NodeDatabases(serverClient)
export const serverStorage = new NodeStorage(serverClient)
export const serverUsers = new NodeUsers(serverClient)

// Configuration constants
export const DATABASE_ID = process.env.APPWRITE_DATABASE_ID!
export const STORAGE_BUCKET_ID = process.env.APPWRITE_STORAGE_BUCKET_ID!

// Collection IDs (will be created during migration)
export const COLLECTIONS = {
  STORES: 'stores',
  CUSTOMERS: 'customers', 
  ORDERS: 'orders',
  INVOICES: 'invoices',
  INVOICE_ITEMS: 'invoice_items',
  STORE_PRICING: 'store_pricing',
  PRICING_TIERS: 'pricing_tiers',
  CUSTOMER_ADDRESSES: 'customer_addresses',
  CUSTOMER_COMMUNICATIONS: 'customer_communications',
  ORDER_ATTACHMENTS: 'order_attachments',
  ORDER_METRICS: 'order_metrics',
  ORDER_NOTES: 'order_notes',
  ORDER_STATUS_HISTORY: 'order_status_history',
  ORDER_TAGS: 'order_tags',
  ORDER_TIMELINE: 'order_timeline',
  INVOICE_ATTACHMENTS: 'invoice_attachments',
  INVOICE_COMMUNICATIONS: 'invoice_communications',
  INVOICE_PAYMENTS: 'invoice_payments',
  STORE_CONFIGURATIONS: 'store_configurations'
} as const

// Type definitions for Appwrite documents
export interface AppwriteDocument {
  $id: string
  $createdAt: string
  $updatedAt: string
  $permissions: string[]
  $collectionId: string
  $databaseId: string
}

// Helper function to convert Prisma data to Appwrite format
export function convertToAppwriteDocument<T extends Record<string, any>>(
  data: T,
  excludeFields: string[] = ['id', 'createdAt', 'updatedAt']
): Omit<T, typeof excludeFields[number]> {
  const converted = { ...data }
  
  // Remove fields that Appwrite handles automatically
  excludeFields.forEach(field => {
    delete converted[field]
  })
  
  return converted
}

// Helper function to convert Appwrite document to app format
export function convertFromAppwriteDocument<T>(
  doc: AppwriteDocument & Record<string, any>
): T & { id: string; createdAt: string; updatedAt: string } {
  const { $id, $createdAt, $updatedAt, $permissions, $collectionId, $databaseId, ...data } = doc
  
  return {
    ...data,
    id: $id,
    createdAt: $createdAt,
    updatedAt: $updatedAt
  } as T & { id: string; createdAt: string; updatedAt: string }
}

// Query helper functions
export const Query = {
  equal: (attribute: string, value: any) => `equal("${attribute}", ${JSON.stringify(value)})`,
  notEqual: (attribute: string, value: any) => `notEqual("${attribute}", ${JSON.stringify(value)})`,
  lessThan: (attribute: string, value: any) => `lessThan("${attribute}", ${JSON.stringify(value)})`,
  lessThanEqual: (attribute: string, value: any) => `lessThanEqual("${attribute}", ${JSON.stringify(value)})`,
  greaterThan: (attribute: string, value: any) => `greaterThan("${attribute}", ${JSON.stringify(value)})`,
  greaterThanEqual: (attribute: string, value: any) => `greaterThanEqual("${attribute}", ${JSON.stringify(value)})`,
  search: (attribute: string, value: string) => `search("${attribute}", "${value}")`,
  isNull: (attribute: string) => `isNull("${attribute}")`,
  isNotNull: (attribute: string) => `isNotNull("${attribute}")`,
  between: (attribute: string, start: any, end: any) => `between("${attribute}", ${JSON.stringify(start)}, ${JSON.stringify(end)})`,
  startsWith: (attribute: string, value: string) => `startsWith("${attribute}", "${value}")`,
  endsWith: (attribute: string, value: string) => `endsWith("${attribute}", "${value}")`,
  select: (attributes: string[]) => `select([${attributes.map(attr => `"${attr}"`).join(', ')}])`,
  orderAsc: (attribute: string) => `orderAsc("${attribute}")`,
  orderDesc: (attribute: string) => `orderDesc("${attribute}")`,
  limit: (limit: number) => `limit(${limit})`,
  offset: (offset: number) => `offset(${offset})`,
  contains: (attribute: string, value: string) => `contains("${attribute}", "${value}")`,
  or: (queries: string[]) => `or([${queries.join(', ')}])`,
  and: (queries: string[]) => `and([${queries.join(', ')}])`
}

// Permission helpers
export const Permission = {
  read: (role: string) => `read("${role}")`,
  write: (role: string) => `write("${role}")`,
  create: (role: string) => `create("${role}")`,
  update: (role: string) => `update("${role}")`,
  delete: (role: string) => `delete("${role}")`,
  
  // Common permission patterns
  public: () => [Permission.read('any')],
  userOnly: (userId: string) => [
    Permission.read(`user:${userId}`),
    Permission.write(`user:${userId}`)
  ],
  authenticated: () => [
    Permission.read('users'),
    Permission.write('users')
  ]
}
