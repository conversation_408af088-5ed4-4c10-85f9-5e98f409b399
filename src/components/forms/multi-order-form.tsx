'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Combobox } from '@/components/ui/combobox'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { LuUpload, LuPlus, LuTriangleAlert, LuLoader, LuX, LuCalculator } from 'react-icons/lu'
import { useAppStore } from '@/lib/store'
import { getUsageUnitOptions } from '@/lib/usage-units'
import { handleNumberInputChange } from '@/lib/utils'

// Pricing calculation result interface
interface PricingCalculationResult {
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  breakdown: {
    storePrice: number
    markup: number
    serviceFee: number
    total: number
  }
  appliedTier: {
    minPrice: number
    maxPrice: number | null
    markupType: string
    markupValue: number
  } | null
  serviceFee: number
}


// Single order schema
const singleOrderSchema = z.object({
  productName: z.string()
    .min(1, 'Product name is required')
    .max(255, 'Product name must be less than 255 characters')
    .trim(),
  quantity: z.number()
    .min(1, 'Quantity must be at least 1')
    .max(9999, 'Quantity cannot exceed 9999')
    .int('Quantity must be a whole number'),
  usageUnit: z.string().optional(),
  comment: z.string()
    .max(500, 'Comment must be less than 500 characters')
    .optional(),
  storePrice: z.number()
    .min(0, 'Store price cannot be negative')
    .max(999999.99, 'Store price cannot exceed ₱999,999.99'),
  pasabuyFee: z.number()
    .min(0, 'Pasabuy fee cannot be negative')
    .max(999999.99, 'Pasabuy fee cannot exceed ₱999,999.99'),
  customerPrice: z.number()
    .min(0, 'Customer price cannot be negative')
    .max(999999.99, 'Customer price cannot exceed ₱999,999.99'),
  storeCodeId: z.string().optional(),
  customerId: z.string().optional(),
  imageFile: z.instanceof(File).optional(),
})

// Multi-order schema with shared customer
const multiOrderSchema = z.object({
  customerId: z.string().optional(),
  orders: z.array(singleOrderSchema)
    .min(1, 'At least one order is required')
    .max(50, 'Cannot exceed 50 orders at once'),
})

type SingleOrderData = z.infer<typeof singleOrderSchema>
type MultiOrderData = z.infer<typeof multiOrderSchema>

interface MultiOrderFormProps {
  onSubmit: (data: MultiOrderData) => Promise<void>
  onCancel: () => void
  initialOrders?: Partial<SingleOrderData>[]
  isLoading?: boolean
}

export function MultiOrderForm({
  onSubmit,
  onCancel,
  initialOrders = [],
  isLoading = false
}: MultiOrderFormProps) {
  const { storeCodes, customers, orders, setStoreCodes, setCustomers, setOrders, addStoreCode, addCustomer } = useAppStore()
  const [usageUnits, setUsageUnits] = useState<string[]>([])
  const [isCreatingStoreCode, setIsCreatingStoreCode] = useState(false)
  const [isCreatingCustomer, setIsCreatingCustomer] = useState(false)
  const [isCreatingUsageUnit, setIsCreatingUsageUnit] = useState(false)

  // Pricing calculation state
  const [autoCalculatedFields, setAutoCalculatedFields] = useState<Map<number, Set<string>>>(new Map())
  const [calculatingPricing, setCalculatingPricing] = useState<Set<number>>(new Set())

  // Form submission states
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  // Image files state for each order
  const [imageFiles, setImageFiles] = useState<Map<number, File>>(new Map())
  const [imagePreviews, setImagePreviews] = useState<Map<number, string>>(new Map())

  const form = useForm<MultiOrderData>({
    resolver: zodResolver(multiOrderSchema),
    defaultValues: {
      customerId: '',
      orders: initialOrders.length > 0 ? initialOrders.map(order => ({
        productName: '',
        quantity: 1,
        usageUnit: '',
        comment: '',
        storePrice: 0,
        pasabuyFee: 0,
        customerPrice: 0,
        storeCodeId: '',
        customerId: '',
        ...order,
      })) : [{
        productName: '',
        quantity: 1,
        usageUnit: '',
        comment: '',
        storePrice: 0,
        pasabuyFee: 0,
        customerPrice: 0,
        storeCodeId: '',
        customerId: '',
      }],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'orders',
  })

  // Pricing calculation function
  const calculatePricing = useCallback(async (orderIndex: number, storePrice: number, storeCodeId?: string) => {
    if (storePrice <= 0) return

    try {
      // Mark this order as calculating
      setCalculatingPricing(prev => new Set(prev).add(orderIndex))

      const response = await fetch('/api/pricing/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          storePrice,
          storeCodeId: storeCodeId ? parseInt(storeCodeId) : undefined
        })
      })

      if (!response.ok) {
        throw new Error('Failed to calculate price')
      }

      const result: PricingCalculationResult = await response.json()

      // Update form values with calculated prices
      form.setValue(`orders.${orderIndex}.pasabuyFee`, result.pasabuyFee)
      form.setValue(`orders.${orderIndex}.customerPrice`, result.customerPrice)

      // Mark these fields as auto-calculated
      setAutoCalculatedFields(prev => {
        const newMap = new Map(prev)
        const orderFields = newMap.get(orderIndex) || new Set()
        orderFields.add('pasabuyFee')
        orderFields.add('customerPrice')
        newMap.set(orderIndex, orderFields)
        return newMap
      })

    } catch (error) {
      console.error('Error calculating pricing:', error)
    } finally {
      // Remove from calculating set
      setCalculatingPricing(prev => {
        const newSet = new Set(prev)
        newSet.delete(orderIndex)
        return newSet
      })
    }
  }, [form])

  // Mark field as manually edited (remove from auto-calculated)
  const markFieldAsManuallyEdited = useCallback((orderIndex: number, fieldName: string) => {
    setAutoCalculatedFields(prev => {
      const newMap = new Map(prev)
      const orderFields = newMap.get(orderIndex) || new Set()
      orderFields.delete(fieldName)
      if (orderFields.size === 0) {
        newMap.delete(orderIndex)
      } else {
        newMap.set(orderIndex, orderFields)
      }
      return newMap
    })
  }, [])

  // Load store codes, customers, orders, and usage units for autocomplete
  useEffect(() => {
    async function loadData() {
      try {
        const [storeCodesRes, customersRes, ordersRes] = await Promise.all([
          fetch('/api/store-codes'),
          fetch('/api/customers'),
          fetch('/api/orders')
        ])

        if (storeCodesRes.ok) {
          const storeCodesData = await storeCodesRes.json()
          setStoreCodes(storeCodesData)
        }

        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(customersData)
        }

        if (ordersRes.ok) {
          const ordersData = await ordersRes.json()

          // Handle both legacy and advanced response formats
          let ordersArray: any[]
          if (Array.isArray(ordersData)) {
            ordersArray = ordersData
          } else if (ordersData && ordersData.data && Array.isArray(ordersData.data)) {
            ordersArray = ordersData.data
          } else {
            ordersArray = []
          }

          setOrders(ordersArray)

          // Extract unique usage units from existing orders
          const existingUsageUnits = [...new Set(
            ordersArray
              .map((order: any) => order.usageUnit)
              .filter((unit: string | null | undefined): unit is string =>
                unit != null && unit.trim() !== ''
              )
          )]

          // Combine predefined units with existing units
          const predefinedUnits = getUsageUnitOptions().map(option => option.value)
          const allUnits = [...new Set([...predefinedUnits, ...existingUsageUnits])]
          setUsageUnits(allUnits)
        }
      } catch (error) {
        console.error('Error loading form data:', error)
      }
    }

    if (storeCodes.length === 0 || customers.length === 0 || orders.length === 0 || usageUnits.length === 0) {
      loadData()
    }
  }, [storeCodes.length, customers.length, orders.length, usageUnits.length, setStoreCodes, setCustomers, setOrders])

  const addOrder = () => {
    append({
      productName: '',
      quantity: 1,
      usageUnit: '',
      comment: '',
      storePrice: 0,
      pasabuyFee: 0,
      customerPrice: 0,
      storeCodeId: '',
      customerId: '', // This will be overridden by the shared customer
    })
  }

  const removeOrder = (index: number) => {
    remove(index)
    // Clean up image files and previews for removed order
    const newImageFiles = new Map(imageFiles)
    const newImagePreviews = new Map(imagePreviews)
    newImageFiles.delete(index)
    newImagePreviews.delete(index)
    setImageFiles(newImageFiles)
    setImagePreviews(newImagePreviews)
  }

  const handleImageChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const newImageFiles = new Map(imageFiles)
      const newImagePreviews = new Map(imagePreviews)

      newImageFiles.set(index, file)

      const reader = new FileReader()
      reader.onload = (e) => {
        newImagePreviews.set(index, e.target?.result as string)
        setImagePreviews(new Map(newImagePreviews))
      }
      reader.readAsDataURL(file)

      setImageFiles(newImageFiles)
    }
  }

  const removeImage = (index: number) => {
    const newImageFiles = new Map(imageFiles)
    const newImagePreviews = new Map(imagePreviews)
    newImageFiles.delete(index)
    newImagePreviews.delete(index)
    setImageFiles(newImageFiles)
    setImagePreviews(newImagePreviews)

    // Clear the file input
    const fileInput = document.getElementById(`image-upload-${index}`) as HTMLInputElement
    if (fileInput) {
      fileInput.value = ''
    }
  }

  const triggerImageUpload = (index: number) => {
    const fileInput = document.getElementById(`image-upload-${index}`) as HTMLInputElement
    if (fileInput) {
      fileInput.click()
    }
  }

  const handleCreateStoreCode = async (searchTerm: string) => {
    try {
      setIsCreatingStoreCode(true)
      const response = await fetch('/api/store-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: searchTerm.toUpperCase(),
          name: searchTerm
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create store code')
      }

      const newStoreCode = await response.json()
      addStoreCode(newStoreCode)
      return newStoreCode.id.toString()
    } catch (error) {
      console.error('Error creating store code:', error)
      throw error
    } finally {
      setIsCreatingStoreCode(false)
    }
  }

  const handleCreateCustomer = async (searchTerm: string) => {
    try {
      setIsCreatingCustomer(true)
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: searchTerm.trim() }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create customer')
      }

      const newCustomer = await response.json()
      addCustomer(newCustomer)
      return newCustomer.id.toString()
    } catch (error) {
      console.error('Error creating customer:', error)
      throw error
    } finally {
      setIsCreatingCustomer(false)
    }
  }

  const handleCreateUsageUnit = async (searchTerm: string) => {
    try {
      setIsCreatingUsageUnit(true)
      const newUnit = searchTerm.trim()
      if (newUnit && !usageUnits.includes(newUnit)) {
        setUsageUnits(prev => [...prev, newUnit])
      }
    } catch (error) {
      console.error('Error creating usage unit:', error)
    } finally {
      setIsCreatingUsageUnit(false)
    }
  }

  const handleSubmit = async (data: MultiOrderData) => {
    try {
      setIsSubmitting(true)
      setSubmitError(null)
      setSubmitSuccess(false)

      // Attach image files and shared customer to each order
      const processedData = {
        ...data,
        orders: data.orders.map((order, index) => ({
          ...order,
          usageUnit: order.usageUnit?.trim() || undefined,
          comment: order.comment?.trim() || undefined,
          storeCodeId: order.storeCodeId?.trim() || undefined,
          customerId: data.customerId?.trim() || undefined, // Use shared customer ID
          imageFile: imageFiles.get(index) || undefined
        }))
      }

      await onSubmit(processedData)
      setSubmitSuccess(true)
    } catch (error) {
      console.error('Form submission error:', error)
      setSubmitError(error instanceof Error ? error.message : 'An unexpected error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Prepare options for comboboxes
  const storeCodeOptions = storeCodes.map(storeCode => ({
    value: storeCode.id.toString(),
    label: `${storeCode.name || storeCode.code} (${storeCode.code})`
  }))

  const customerOptions = customers.map(customer => ({
    value: customer.id.toString(),
    label: customer.name
  }))

  // Get unique product names from existing orders, sorted alphabetically
  const productNameOptions = React.useMemo(() => {
    if (!Array.isArray(orders)) return []

    const uniqueNames = Array.from(new Set(orders.map(order => order.productName)))
      .filter(name => name && name.trim().length > 0)
      .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))

    return uniqueNames.map(name => ({
      value: name,
      label: name
    }))
  }, [orders])

  const usageUnitOptions = usageUnits.map(unit => ({
    value: unit,
    label: unit
  }))

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        {/* Shared Customer Selection */}
        <Card className="p-3">
          <div className="space-y-3">
            <h3 className="text-base font-medium">Customer Information</h3>
            <FormField
              control={form.control}
              name="customerId"
              render={({ field }) => (
                <FormItem>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Customer</label>
                    <FormControl>
                      <Combobox
                        options={customerOptions}
                        value={field.value || ""}
                        onValueChange={async (value) => {
                          if (value.startsWith('__create__')) {
                            const searchTerm = value.replace('__create__', '')
                            try {
                              const newId = await handleCreateCustomer(searchTerm)
                              field.onChange(newId)
                            } catch (error) {
                              console.error('Failed to create customer:', error)
                            }
                          } else {
                            field.onChange(value)
                          }
                        }}
                        placeholder="Customer..."
                        searchPlaceholder="Search customers..."
                        emptyText="No customers found."
                        onCreateNew={handleCreateCustomer}
                        isCreating={isCreatingCustomer}
                        className="h-8"
                      />
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          </div>
        </Card>

        {/* Orders Section */}
        <Card className="p-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-medium">Orders</h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addOrder}
              className="min-h-[44px] px-4"
            >
              <LuPlus className="h-4 w-4 mr-1" />
              Add Order
            </Button>
          </div>

          {/* Mobile Layout: Card-based (< md) */}
          <div className="md:hidden space-y-4">
            {fields.map((field, index) => (
              <Card key={field.id} className="p-4 border border-border">
                <div className="space-y-4">
                  {/* Order Header */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">
                      Order {index + 1}
                    </span>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeOrder(index)}
                        className="min-h-[44px] min-w-[44px] p-0 text-destructive hover:text-destructive"
                        title="Remove order"
                        aria-label="Remove order"
                      >
                        <LuX className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {/* Image Upload Section */}
                  <div className="flex justify-center">
                    <div className="w-20 h-20">
                      {imagePreviews.get(index) ? (
                        <div className="relative w-20 h-20">
                          <img
                            src={imagePreviews.get(index)!}
                            alt={`Product ${index + 1} preview`}
                            className="w-20 h-20 object-cover rounded-lg border border-border"
                          />
                          <div className="absolute -top-1 -right-1 flex gap-1">
                            <button
                              type="button"
                              onClick={() => triggerImageUpload(index)}
                              className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs hover:bg-primary/90 transition-colors"
                              title="Change image"
                              aria-label="Change image"
                            >
                              <LuUpload className="h-3 w-3" />
                            </button>
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="w-6 h-6 bg-destructive text-destructive-foreground rounded-full flex items-center justify-center text-xs hover:bg-destructive/90 transition-colors"
                              title="Remove image"
                              aria-label="Remove image"
                            >
                              <LuX className="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div
                          className="w-20 h-20 border-2 border-dashed border-border rounded-lg flex items-center justify-center cursor-pointer hover:border-border/80 transition-colors"
                          onClick={() => triggerImageUpload(index)}
                        >
                          <LuUpload className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                      <Input
                        id={`image-upload-${index}`}
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => handleImageChange(index, e)}
                      />
                    </div>
                  </div>

                  {/* Product Name */}
                  <FormField
                    control={form.control}
                    name={`orders.${index}.productName`}
                    render={({ field }) => (
                      <FormItem>
                        <label className="text-sm font-medium">Product Name *</label>
                        <FormControl>
                          <Combobox
                            options={productNameOptions}
                            value={field.value || ""}
                            onValueChange={field.onChange}
                            placeholder="Product name..."
                            searchPlaceholder="Search products..."
                            emptyText="No products found."
                            mode="autocomplete"
                            className="min-h-[44px] text-base"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Quantity and Usage Unit Row */}
                  <div className="grid grid-cols-2 gap-3">
                    <FormField
                      control={form.control}
                      name={`orders.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <label className="text-sm font-medium">Quantity *</label>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              max="9999"
                              className="min-h-[44px] text-base text-center"
                              {...field}
                              onChange={(e) => field.onChange(handleNumberInputChange(e.target.value, {
                                integer: true,
                                min: 1,
                                defaultValue: 1
                              }))}
                              onBlur={(e) => {
                                // Convert to number on blur if it's a valid string
                                const value = e.target.value
                                if (value === '' || isNaN(parseInt(value))) {
                                  field.onChange(1)
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`orders.${index}.usageUnit`}
                      render={({ field }) => (
                        <FormItem>
                          <label className="text-sm font-medium">Unit</label>
                          <FormControl>
                            <Combobox
                              options={usageUnitOptions}
                              value={field.value || ""}
                              onValueChange={field.onChange}
                              placeholder="Unit..."
                              searchPlaceholder="Search units..."
                              emptyText="No units found."
                              onCreateNew={handleCreateUsageUnit}
                              isCreating={isCreatingUsageUnit}
                              className="min-h-[44px] text-base"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Pricing Row */}
                  <div className="grid grid-cols-1 gap-3">
                    <FormField
                      control={form.control}
                      name={`orders.${index}.storePrice`}
                      render={({ field }) => (
                        <FormItem>
                          <label className="text-sm font-medium">Store Price *</label>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-base">₱</span>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                className="min-h-[44px] text-base pl-8"
                                {...field}
                                onChange={(e) => {
                                  const newValue = handleNumberInputChange(e.target.value, {
                                    min: 0,
                                    defaultValue: 0
                                  })
                                  field.onChange(newValue)

                                  // Trigger pricing calculation if value is valid
                                  const numericValue = typeof newValue === 'number' ? newValue : parseFloat(newValue.toString())
                                  if (numericValue > 0) {
                                    const storeCodeId = form.getValues(`orders.${index}.storeCodeId`)
                                    calculatePricing(index, numericValue, storeCodeId as string)
                                  }
                                }}
                                onBlur={(e) => {
                                  // Convert to number on blur if it's a valid string
                                  const value = e.target.value
                                  if (value === '' || isNaN(parseFloat(value))) {
                                    field.onChange(0)
                                  }
                                }}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`orders.${index}.pasabuyFee`}
                      render={({ field }) => {
                        const isAutoCalculated = autoCalculatedFields.get(index)?.has('pasabuyFee')
                        const isCalculating = calculatingPricing.has(index)

                        return (
                          <FormItem>
                            <div className="flex items-center gap-2">
                              <label className="text-sm font-medium">Pasabuy Fee *</label>
                              {isAutoCalculated && (
                                <Badge variant="secondary" className="text-xs">
                                  <LuCalculator className="h-3 w-3 mr-1" />
                                  Auto
                                </Badge>
                              )}
                              {isCalculating && (
                                <LuLoader className="h-3 w-3 animate-spin text-muted-foreground" />
                              )}
                            </div>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-base">₱</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className={`min-h-[44px] text-base pl-8 ${isAutoCalculated ? 'bg-blue-50 border-blue-200' : ''}`}
                                  {...field}
                                  onChange={(e) => {
                                    const newValue = handleNumberInputChange(e.target.value, {
                                      min: 0,
                                      defaultValue: 0
                                    })
                                    field.onChange(newValue)
                                    // Mark as manually edited when user changes the value
                                    markFieldAsManuallyEdited(index, 'pasabuyFee')
                                  }}
                                  onBlur={(e) => {
                                    // Convert to number on blur if it's a valid string
                                    const value = e.target.value
                                    if (value === '' || isNaN(parseFloat(value))) {
                                      field.onChange(0)
                                    }
                                  }}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />

                    <FormField
                      control={form.control}
                      name={`orders.${index}.customerPrice`}
                      render={({ field }) => {
                        const isAutoCalculated = autoCalculatedFields.get(index)?.has('customerPrice')
                        const isCalculating = calculatingPricing.has(index)

                        return (
                          <FormItem>
                            <div className="flex items-center gap-2">
                              <label className="text-sm font-medium">Customer Price *</label>
                              {isAutoCalculated && (
                                <Badge variant="secondary" className="text-xs">
                                  <LuCalculator className="h-3 w-3 mr-1" />
                                  Auto
                                </Badge>
                              )}
                              {isCalculating && (
                                <LuLoader className="h-3 w-3 animate-spin text-muted-foreground" />
                              )}
                            </div>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-base">₱</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className={`min-h-[44px] text-base pl-8 ${isAutoCalculated ? 'bg-blue-50 border-blue-200' : ''}`}
                                  {...field}
                                  onChange={(e) => {
                                    const newValue = handleNumberInputChange(e.target.value, {
                                      min: 0,
                                      defaultValue: 0
                                    })
                                    field.onChange(newValue)
                                    // Mark as manually edited when user changes the value
                                    markFieldAsManuallyEdited(index, 'customerPrice')
                                  }}
                                  onBlur={(e) => {
                                    // Convert to number on blur if it's a valid string
                                    const value = e.target.value
                                    if (value === '' || isNaN(parseFloat(value))) {
                                      field.onChange(0)
                                    }
                                  }}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </div>

                  {/* Store and Comment Row */}
                  <div className="grid grid-cols-1 gap-3">
                    <FormField
                      control={form.control}
                      name={`orders.${index}.storeCodeId`}
                      render={({ field }) => (
                        <FormItem>
                          <label className="text-sm font-medium">Store</label>
                          <FormControl>
                            <Combobox
                              options={storeCodeOptions}
                              value={field.value || ""}
                              onValueChange={async (value) => {
                                if (value.startsWith('__create__')) {
                                  const searchTerm = value.replace('__create__', '')
                                  try {
                                    const newId = await handleCreateStoreCode(searchTerm)
                                    field.onChange(newId)

                                    // Trigger pricing recalculation with new store
                                    const storePrice = form.getValues(`orders.${index}.storePrice`)
                                    const numericStorePrice = typeof storePrice === 'number' ? storePrice : parseFloat(String(storePrice || 0))
                                    if (numericStorePrice > 0) {
                                      calculatePricing(index, numericStorePrice, newId)
                                    }
                                  } catch (error) {
                                    console.error('Failed to create store code:', error)
                                  }
                                } else {
                                  field.onChange(value)

                                  // Trigger pricing recalculation with new store
                                  const storePrice = form.getValues(`orders.${index}.storePrice`)
                                  const numericStorePrice = typeof storePrice === 'number' ? storePrice : parseFloat(String(storePrice || 0))
                                  if (numericStorePrice > 0) {
                                    calculatePricing(index, numericStorePrice, value)
                                  }
                                }
                              }}
                              placeholder="Store..."
                              searchPlaceholder="Search stores..."
                              emptyText="No stores found."
                              onCreateNew={handleCreateStoreCode}
                              isCreating={isCreatingStoreCode}
                              className="min-h-[44px] text-base"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`orders.${index}.comment`}
                      render={({ field }) => (
                        <FormItem>
                          <label className="text-sm font-medium">Comment</label>
                          <FormControl>
                            <Input
                              placeholder="Comment..."
                              className="min-h-[44px] text-base"
                              maxLength={500}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Desktop Layout: Table-based (≥ md) */}
          <div className="hidden md:block overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-8"></TableHead>
                  <TableHead className="min-w-[120px]">Image</TableHead>
                  <TableHead className="min-w-[150px]">Product Name</TableHead>
                  <TableHead className="w-20">Qty</TableHead>
                  <TableHead className="min-w-[100px]">Unit</TableHead>
                  <TableHead className="min-w-[100px]">Store Price</TableHead>
                  <TableHead className="min-w-[100px]">Fee</TableHead>
                  <TableHead className="min-w-[100px]">Customer Price</TableHead>
                  <TableHead className="min-w-[120px]">Store</TableHead>
                  <TableHead className="min-w-[150px]">Comment</TableHead>
                  <TableHead className="w-8"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {fields.map((field, index) => (
                  <TableRow key={field.id}>
                    <TableCell className="text-center text-sm text-muted-foreground">
                      {index + 1}
                    </TableCell>

                    {/* Image Upload Cell */}
                    <TableCell>
                      <div className="w-16 h-16">
                        {imagePreviews.get(index) ? (
                          <div className="relative w-16 h-16">
                            <img
                              src={imagePreviews.get(index)!}
                              alt={`Product ${index + 1} preview`}
                              className="w-16 h-16 object-cover rounded-lg border border-border"
                            />
                            <div className="absolute -top-1 -right-1 flex gap-1">
                              <button
                                type="button"
                                onClick={() => triggerImageUpload(index)}
                                className="w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs hover:bg-primary/90 transition-colors"
                                title="Change image"
                              >
                                <LuUpload className="h-3 w-3" />
                              </button>
                              <button
                                type="button"
                                onClick={() => removeImage(index)}
                                className="w-5 h-5 bg-destructive text-destructive-foreground rounded-full flex items-center justify-center text-xs hover:bg-destructive/90 transition-colors"
                                title="Remove image"
                              >
                                <LuX className="h-3 w-3" />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div
                            className="w-16 h-16 border-2 border-dashed border-border rounded-lg flex items-center justify-center cursor-pointer hover:border-border/80 transition-colors"
                            onClick={() => triggerImageUpload(index)}
                          >
                            <LuUpload className="h-4 w-4 text-muted-foreground" />
                          </div>
                        )}
                        <Input
                          id={`image-upload-${index}`}
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => handleImageChange(index, e)}
                        />
                      </div>
                    </TableCell>

                    {/* Product Name Cell */}
                    <TableCell>
                      <FormField
                        control={form.control}
                        name={`orders.${index}.productName`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Combobox
                                options={productNameOptions}
                                value={field.value || ""}
                                onValueChange={field.onChange}
                                placeholder="Product name..."
                                searchPlaceholder="Search products..."
                                emptyText="No products found."
                                mode="autocomplete"
                                className="h-7 text-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </TableCell>

                    {/* Quantity Cell */}
                    <TableCell>
                      <FormField
                        control={form.control}
                        name={`orders.${index}.quantity`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                max="9999"
                                className="h-7 text-sm text-center"
                                {...field}
                                onChange={(e) => field.onChange(handleNumberInputChange(e.target.value, {
                                  integer: true,
                                  min: 1,
                                  defaultValue: 1
                                }))}
                                onBlur={(e) => {
                                  // Convert to number on blur if it's a valid string
                                  const value = e.target.value
                                  if (value === '' || isNaN(parseInt(value))) {
                                    field.onChange(1)
                                  }
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </TableCell>

                    {/* Usage Unit Cell */}
                    <TableCell>
                      <FormField
                        control={form.control}
                        name={`orders.${index}.usageUnit`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Combobox
                                options={usageUnitOptions}
                                value={field.value || ""}
                                onValueChange={field.onChange}
                                placeholder="Unit..."
                                searchPlaceholder="Search units..."
                                emptyText="No units found."
                                onCreateNew={handleCreateUsageUnit}
                                isCreating={isCreatingUsageUnit}
                                className="h-7 text-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </TableCell>

                    {/* Store Price Cell */}
                    <TableCell>
                      <FormField
                        control={form.control}
                        name={`orders.${index}.storePrice`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <div className="relative">
                                <span className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">₱</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  className="h-7 text-sm pl-6"
                                  {...field}
                                  onChange={(e) => {
                                    const newValue = handleNumberInputChange(e.target.value, {
                                      min: 0,
                                      defaultValue: 0
                                    })
                                    field.onChange(newValue)

                                    // Trigger pricing calculation if value is valid
                                    const numericValue = typeof newValue === 'number' ? newValue : parseFloat(newValue.toString())
                                    if (numericValue > 0) {
                                      const storeCodeId = form.getValues(`orders.${index}.storeCodeId`)
                                      calculatePricing(index, numericValue, storeCodeId as string)
                                    }
                                  }}
                                  onBlur={(e) => {
                                    // Convert to number on blur if it's a valid string
                                    const value = e.target.value
                                    if (value === '' || isNaN(parseFloat(value))) {
                                      field.onChange(0)
                                    }
                                  }}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </TableCell>

                    {/* Pasabuy Fee Cell */}
                    <TableCell>
                      <FormField
                        control={form.control}
                        name={`orders.${index}.pasabuyFee`}
                        render={({ field }) => {
                          const isAutoCalculated = autoCalculatedFields.get(index)?.has('pasabuyFee')
                          const isCalculating = calculatingPricing.has(index)

                          return (
                            <FormItem>
                              <FormControl>
                                <div className="relative">
                                  <span className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">₱</span>
                                  {isAutoCalculated && (
                                    <LuCalculator className="absolute right-2 top-1/2 -translate-y-1/2 h-3 w-3 text-blue-500" />
                                  )}
                                  {isCalculating && (
                                    <LuLoader className="absolute right-2 top-1/2 -translate-y-1/2 h-3 w-3 animate-spin text-muted-foreground" />
                                  )}
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    className={`h-7 text-sm pl-6 ${isCalculating || isAutoCalculated ? 'pr-8' : ''} ${isAutoCalculated ? 'bg-blue-50 border-blue-200' : ''}`}
                                    {...field}
                                    onChange={(e) => {
                                      const newValue = handleNumberInputChange(e.target.value, {
                                        min: 0,
                                        defaultValue: 0
                                      })
                                      field.onChange(newValue)
                                      // Mark as manually edited when user changes the value
                                      markFieldAsManuallyEdited(index, 'pasabuyFee')
                                    }}
                                    onBlur={(e) => {
                                      // Convert to number on blur if it's a valid string
                                      const value = e.target.value
                                      if (value === '' || isNaN(parseFloat(value))) {
                                        field.onChange(0)
                                      }
                                    }}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )
                        }}
                      />
                    </TableCell>

                    {/* Customer Price Cell */}
                    <TableCell>
                      <FormField
                        control={form.control}
                        name={`orders.${index}.customerPrice`}
                        render={({ field }) => {
                          const isAutoCalculated = autoCalculatedFields.get(index)?.has('customerPrice')
                          const isCalculating = calculatingPricing.has(index)

                          return (
                            <FormItem>
                              <FormControl>
                                <div className="relative">
                                  <span className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">₱</span>
                                  {isAutoCalculated && (
                                    <LuCalculator className="absolute right-2 top-1/2 -translate-y-1/2 h-3 w-3 text-blue-500" />
                                  )}
                                  {isCalculating && (
                                    <LuLoader className="absolute right-2 top-1/2 -translate-y-1/2 h-3 w-3 animate-spin text-muted-foreground" />
                                  )}
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    className={`h-7 text-sm pl-6 ${isCalculating || isAutoCalculated ? 'pr-8' : ''} ${isAutoCalculated ? 'bg-blue-50 border-blue-200' : ''}`}
                                    {...field}
                                    onChange={(e) => {
                                      const newValue = handleNumberInputChange(e.target.value, {
                                        min: 0,
                                        defaultValue: 0
                                      })
                                      field.onChange(newValue)
                                      // Mark as manually edited when user changes the value
                                      markFieldAsManuallyEdited(index, 'customerPrice')
                                    }}
                                    onBlur={(e) => {
                                      // Convert to number on blur if it's a valid string
                                      const value = e.target.value
                                      if (value === '' || isNaN(parseFloat(value))) {
                                        field.onChange(0)
                                      }
                                    }}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )
                        }}
                      />
                    </TableCell>

                    {/* Store Code Cell */}
                    <TableCell>
                      <FormField
                        control={form.control}
                        name={`orders.${index}.storeCodeId`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Combobox
                                options={storeCodeOptions}
                                value={field.value || ""}
                                onValueChange={async (value) => {
                                  if (value.startsWith('__create__')) {
                                    const searchTerm = value.replace('__create__', '')
                                    try {
                                      const newId = await handleCreateStoreCode(searchTerm)
                                      field.onChange(newId)

                                      // Trigger pricing recalculation with new store
                                      const storePrice = form.getValues(`orders.${index}.storePrice`)
                                      const numericStorePrice = typeof storePrice === 'number' ? storePrice : parseFloat(String(storePrice || 0))
                                      if (numericStorePrice > 0) {
                                        calculatePricing(index, numericStorePrice, newId)
                                      }
                                    } catch (error) {
                                      console.error('Failed to create store code:', error)
                                    }
                                  } else {
                                    field.onChange(value)

                                    // Trigger pricing recalculation with new store
                                    const storePrice = form.getValues(`orders.${index}.storePrice`)
                                    const numericStorePrice = typeof storePrice === 'number' ? storePrice : parseFloat(String(storePrice || 0))
                                    if (numericStorePrice > 0) {
                                      calculatePricing(index, numericStorePrice, value)
                                    }
                                  }
                                }}
                                placeholder="Store..."
                                searchPlaceholder="Search stores..."
                                emptyText="No stores found."
                                onCreateNew={handleCreateStoreCode}
                                isCreating={isCreatingStoreCode}
                                className="h-7 text-sm"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </TableCell>

                    {/* Comment Cell */}
                    <TableCell>
                      <FormField
                        control={form.control}
                        name={`orders.${index}.comment`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                placeholder="Comment..."
                                className="h-7 text-sm"
                                maxLength={500}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </TableCell>

                    {/* Remove button */}
                    <TableCell>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeOrder(index)}
                          className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                        >
                          <LuX className="h-4 w-4" />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </Card>

        {/* Form Submission Feedback */}
        {submitError && (
          <Card className="p-3 bg-red-50 border-red-200">
            <div className="flex items-start gap-3">
              <LuTriangleAlert className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-red-800 text-sm">Error Creating Orders</h4>
                <p className="text-red-700 text-sm mt-1">{submitError}</p>
              </div>
            </div>
          </Card>
        )}

        {submitSuccess && (
          <Card className="p-3 bg-green-50 border-green-200">
            <div className="flex items-start gap-3">
              <LuLoader className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-green-800 text-sm">Orders Created Successfully</h4>
                <p className="text-green-700 text-sm mt-1">All {fields.length} orders have been created and are ready for processing.</p>
              </div>
            </div>
          </Card>
        )}

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isLoading}
            className="flex-1 min-h-[48px] text-base font-medium order-2 sm:order-1"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || isLoading}
            className="flex-1 min-h-[48px] text-base font-medium order-1 sm:order-2"
          >
            {(isSubmitting || isLoading) && (
              <LuLoader className="h-4 w-4 mr-2 animate-spin" />
            )}
            {isSubmitting || isLoading ? 'Creating Orders...' : `Create ${fields.length} Orders`}
          </Button>
        </div>
      </form>
    </Form>
  )
}
