'use client'

import { z } from 'zod'
import { useState, useEffect, useCallback, useRef } from 'react'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { BaseForm } from './base-form'
import { handleNumberInputChange } from '@/lib/utils'

// Enhanced store schema - only store code is required, all other fields are optional
const enhancedStoreSchema = z.object({
  // Basic Information - only code is required
  code: z.string()
    .min(1, 'Store code is required')
    .max(20, 'Store code must be 20 characters or less')
    .regex(/^[A-Z0-9_-]+$/, 'Store code must contain only uppercase letters, numbers, underscores, and hyphens'),
  name: z.string().max(255, 'Store name must be 255 characters or less').optional().or(z.literal('')),
  storeType: z.enum(['RETAIL', 'WHOLESALE', 'ONLINE', 'MARKETPLACE', 'WAREHOUSE', 'DISTRIBUTION_CENTER', 'FRANCHISE', 'CORPORATE']).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'MAINTENANCE', 'TEMPORARILY_CLOSED', 'PERMANENTLY_CLOSED', 'UNDER_CONSTRUCTION']).optional(),

  // Hierarchy - all optional
  parentStoreId: z.number().int().positive().optional(),
  storeGroup: z.string().max(100, 'Store group must be 100 characters or less').optional().or(z.literal('')),
  region: z.string().max(100, 'Region must be 100 characters or less').optional().or(z.literal('')),
  district: z.string().max(100, 'District must be 100 characters or less').optional().or(z.literal('')),

  // Location Information - all optional
  address: z.string().max(500, 'Address must be 500 characters or less').optional().or(z.literal('')),
  city: z.string().max(100, 'City must be 100 characters or less').optional().or(z.literal('')),
  state: z.string().max(100, 'State must be 100 characters or less').optional().or(z.literal('')),
  postalCode: z.string().max(20, 'Postal code must be 20 characters or less').optional().or(z.literal('')),
  country: z.string().max(100, 'Country must be 100 characters or less').optional().or(z.literal('')),

  // Contact Information - all optional without format validation
  phone: z.string().max(20, 'Phone must be 20 characters or less').optional().or(z.literal('')),
  email: z.string().max(255, 'Email must be 255 characters or less').optional().or(z.literal('')),
  website: z.string().max(255, 'Website must be 255 characters or less').optional().or(z.literal('')),

  // Management Information - all optional
  managerName: z.string().max(255, 'Manager name must be 255 characters or less').optional().or(z.literal('')),
  managerPhone: z.string().max(20, 'Manager phone must be 20 characters or less').optional().or(z.literal('')),
  managerEmail: z.string().max(255, 'Manager email must be 255 characters or less').optional().or(z.literal('')),
  contactPerson: z.string().max(255, 'Contact person must be 255 characters or less').optional().or(z.literal('')),

  // Operational Information - all optional
  operatingHours: z.string().max(500, 'Operating hours must be 500 characters or less').optional().or(z.literal('')),
  timezone: z.string().max(50, 'Timezone must be 50 characters or less').optional().or(z.literal('')),
  isOpen: z.boolean().optional(),
  allowsPickup: z.boolean().optional(),
  allowsDelivery: z.boolean().optional(),
  deliveryRadius: z.number().min(0, 'Delivery radius must be positive').max(1000, 'Delivery radius cannot exceed 1000km').optional(),

  // Notes - all optional
  notes: z.string().max(1000, 'Notes must be 1000 characters or less').optional().or(z.literal('')),
  internalNotes: z.string().max(1000, 'Internal notes must be 1000 characters or less').optional().or(z.literal('')),
  specialInstructions: z.string().max(1000, 'Special instructions must be 1000 characters or less').optional().or(z.literal('')),
})

type EnhancedStoreFormData = z.infer<typeof enhancedStoreSchema>

interface ValidationResult {
  isValid: boolean
  errors: Array<{
    field: string
    value?: unknown
    errorCode: string
    errorMessage: string
    severity: 'ERROR' | 'WARNING'
  }>
  warnings: Array<{
    field: string
    value?: unknown
    warningCode: string
    warningMessage: string
  }>
}

interface EnhancedStoreFormProps {
  onSubmit: (data: EnhancedStoreFormData) => Promise<void>
  onCancel: () => void
  initialData?: Partial<EnhancedStoreFormData>
  isLoading?: boolean
  enableRealTimeValidation?: boolean
}

export function EnhancedStoreForm({
  onSubmit,
  onCancel,
  initialData,
  isLoading = false,
  enableRealTimeValidation = true
}: EnhancedStoreFormProps) {
  const [, setValidationResult] = useState<ValidationResult | null>(null)
  const [, setIsValidating] = useState(false)
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Helper function to convert null values to empty strings or appropriate defaults
  const sanitizeValue = (value: unknown): string => {
    if (value === null || value === undefined) return ''
    return String(value)
  }

  const sanitizeNumber = (value: unknown): number | undefined => {
    if (value === null || value === undefined) return undefined
    const num = Number(value)
    return isNaN(num) ? undefined : num
  }

  const sanitizeBoolean = (value: unknown): boolean => {
    if (value === null || value === undefined) return true
    return Boolean(value)
  }

  const defaultData = {
    // Basic Information
    code: sanitizeValue(initialData?.code) || '',
    name: sanitizeValue(initialData?.name),
    storeType: initialData?.storeType || 'RETAIL',
    status: initialData?.status || 'ACTIVE',

    // Hierarchy
    parentStoreId: sanitizeNumber(initialData?.parentStoreId),
    storeGroup: sanitizeValue(initialData?.storeGroup),
    region: sanitizeValue(initialData?.region),
    district: sanitizeValue(initialData?.district),

    // Location Information
    address: sanitizeValue(initialData?.address),
    city: sanitizeValue(initialData?.city),
    state: sanitizeValue(initialData?.state),
    postalCode: sanitizeValue(initialData?.postalCode),
    country: sanitizeValue(initialData?.country) || 'Philippines',

    // Contact Information
    phone: sanitizeValue(initialData?.phone),
    email: sanitizeValue(initialData?.email),
    website: sanitizeValue(initialData?.website),

    // Management Information
    managerName: sanitizeValue(initialData?.managerName),
    managerPhone: sanitizeValue(initialData?.managerPhone),
    managerEmail: sanitizeValue(initialData?.managerEmail),
    contactPerson: sanitizeValue(initialData?.contactPerson),

    // Operational Information
    operatingHours: sanitizeValue(initialData?.operatingHours),
    timezone: sanitizeValue(initialData?.timezone) || 'Asia/Manila',
    isOpen: sanitizeBoolean(initialData?.isOpen),
    allowsPickup: sanitizeBoolean(initialData?.allowsPickup),
    allowsDelivery: sanitizeBoolean(initialData?.allowsDelivery),
    deliveryRadius: sanitizeNumber(initialData?.deliveryRadius),

    // Notes
    notes: sanitizeValue(initialData?.notes),
    internalNotes: sanitizeValue(initialData?.internalNotes),
    specialInstructions: sanitizeValue(initialData?.specialInstructions),
  }

  // Debounced validation function
  const debouncedValidateStoreData = useCallback(
    async (data: Partial<EnhancedStoreFormData>) => {
      if (!enableRealTimeValidation) return

      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }

      // Set new timeout
      debounceTimeoutRef.current = setTimeout(async () => {
        setIsValidating(true)
        try {
          const response = await fetch('/api/enhanced/stores/validate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              storeData: {
                ...data,
                id: (initialData as any)?.id // Include the store ID for update validation
              },
              isUpdate: !!initialData?.code
            })
          })

          if (response.ok) {
            const result = await response.json()
            setValidationResult(result)
          }
        } catch (error) {
          console.error('Validation error:', error)
        } finally {
          setIsValidating(false)
        }
      }, 800) // Increased debounce time to 800ms for better performance
    },
    [enableRealTimeValidation, initialData?.code]
  )

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  return (
    <BaseForm
      schema={enhancedStoreSchema}
      onSubmit={onSubmit}
      onCancel={onCancel}
      initialData={defaultData}
      isLoading={isLoading}
      submitButtonText="Save Store"
    >
      {(form) => {
        // Watch specific fields for validation instead of all form values
        const codeValue = form.watch('code')
        const nameValue = form.watch('name')
        const emailValue = form.watch('email')
        const websiteValue = form.watch('website')
        const managerEmailValue = form.watch('managerEmail')

        // Trigger validation when key fields change
        if (enableRealTimeValidation && (codeValue || nameValue || emailValue || websiteValue || managerEmailValue)) {
          const formData = form.getValues()
          debouncedValidateStoreData(formData)
        }

        return (
          <div className="space-y-6">

            {/* Basic Information */}
            <Card className="p-6">
              <h3 className="text-base font-medium mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Store Code *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="STORE-001"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                        />
                      </FormControl>
                      <FormDescription className="hidden sm:block">
                        Required. Unique code to identify the store (uppercase letters, numbers, underscores, hyphens)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Store Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Store name..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="storeType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Store Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select store type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="RETAIL">Retail</SelectItem>
                          <SelectItem value="WHOLESALE">Wholesale</SelectItem>
                          <SelectItem value="ONLINE">Online</SelectItem>
                          <SelectItem value="MARKETPLACE">Marketplace</SelectItem>
                          <SelectItem value="WAREHOUSE">Warehouse</SelectItem>
                          <SelectItem value="DISTRIBUTION_CENTER">Distribution Center</SelectItem>
                          <SelectItem value="FRANCHISE">Franchise</SelectItem>
                          <SelectItem value="CORPORATE">Corporate</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ACTIVE">Active</SelectItem>
                          <SelectItem value="INACTIVE">Inactive</SelectItem>
                          <SelectItem value="MAINTENANCE">Maintenance</SelectItem>
                          <SelectItem value="TEMPORARILY_CLOSED">Temporarily Closed</SelectItem>
                          <SelectItem value="PERMANENTLY_CLOSED">Permanently Closed</SelectItem>
                          <SelectItem value="UNDER_CONSTRUCTION">Under Construction</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </Card>

            {/* Location Information */}
            <Card className="p-6">
              <h3 className="text-base font-medium mb-4">Location Information</h3>
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Street address..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="City"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State/Province</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="State or Province"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="postalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Postal Code</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Postal Code"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Country"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="region"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Region</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Region"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </Card>

            {/* Contact Information */}
            <Card className="p-6">
              <h3 className="text-base font-medium mb-4">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="+63 ************"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Website</FormLabel>
                      <FormControl>
                        <Input
                          type="url"
                          placeholder="https://example.com"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </Card>

            {/* Management Information */}
            <Card className="p-6">
              <h3 className="text-base font-medium mb-4">Management Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="managerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Manager Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Manager name..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="managerPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Manager Phone</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="+63 ************"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="managerEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Manager Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactPerson"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Person</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Contact person..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </Card>

            {/* Operational Information */}
            <Card className="p-6">
              <h3 className="text-base font-medium mb-4">Operational Information</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="operatingHours"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Operating Hours</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Mon-Fri 9:00-18:00"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="timezone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Timezone</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Asia/Manila"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="isOpen"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Currently Open</FormLabel>
                          <FormDescription>
                            Store is currently accepting orders
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="allowsPickup"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Allows Pickup</FormLabel>
                          <FormDescription>
                            Customers can pick up orders
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="allowsDelivery"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Allows Delivery</FormLabel>
                          <FormDescription>
                            Store offers delivery service
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Delivery radius - only show if delivery is enabled */}
                {form.watch('allowsDelivery') && (
                  <FormField
                    control={form.control}
                    name="deliveryRadius"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Delivery Radius (km)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="10"
                            {...field}
                            onChange={(e) => {
                              if (e.target.value === '') {
                                field.onChange(undefined)
                              } else {
                                const value = handleNumberInputChange(e.target.value, {
                                  min: 0,
                                  defaultValue: 0,
                                  allowEmpty: false
                                })
                                field.onChange(value as number)
                              }
                            }}
                            onBlur={(e) => {
                              // Allow empty for optional field
                              if (e.target.value === '') {
                                field.onChange(undefined)
                              } else if (isNaN(parseFloat(e.target.value))) {
                                field.onChange(undefined)
                              }
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Maximum delivery distance in kilometers
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </Card>

            {/* Notes */}
            <Card className="p-6">
              <h3 className="text-base font-medium mb-4">Notes & Instructions</h3>
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Public Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="General notes about this store..."
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Notes visible to customers and staff
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="internalNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Internal Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Internal notes for staff only..."
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Internal notes for staff only (not visible to customers)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="specialInstructions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Special Instructions</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Special handling instructions..."
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Special instructions for order processing
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </Card>
          </div>
        )
      }}
    </BaseForm>
  )
}

// Export both the enhanced form and the original simple form for backward compatibility
export { EnhancedStoreForm as StoreCodeForm }
